<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Pyxi • New or Existing?</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Permanent+Marker&family=Work+Sans:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
    <script>
      tailwind.config = {
        theme: {
          extend: {
            fontFamily: { sans: ["Work Sans", "sans-serif"], marker: ["Permanent Marker", "cursive"] },
            colors: { "pyxi-red": "#F09E5B" }
          }
        }
      };
    </script>
  </head>
  <body class="font-sans bg-[#FFF9EC] min-h-screen flex items-center justify-center py-10 px-4">
    <div class="w-full max-w-lg bg-white rounded-2xl shadow-xl p-6 md:p-8 border border-gray-100">
      <div class="flex flex-col items-center">
        <img src="./images/pyxi-logo.png" alt="Pyxi" class="h-8 w-auto" />
      </div>

      <h1 class="text-center mt-6 text-2xl md:text-3xl font-semibold text-gray-900">Are you a new user or<br/>an existing one?</h1>

      <form class="mt-6" onsubmit="event.preventDefault(); handleSubmit();">
        <label class="block text-sm font-medium text-gray-700">Email</label>
        <input id="email" type="email" required placeholder="<EMAIL>" class="mt-2 w-full px-3 py-2 border rounded-lg" />
        <button class="w-full mt-4 bg-[#F09E5B] text-white font-semibold py-3 rounded-lg hover:opacity-90">Submit</button>
      </form>

      <button id="newUserBtn" class="w-full mt-3 bg-gray-100 text-gray-900 font-medium py-3 rounded-lg hover:bg-gray-200">I'm New User</button>

      <div class="text-center text-xs text-gray-400 mt-6">
        <a href="dinner-invite.html" class="hover:underline">← Back to invite</a>
      </div>
    </div>

    <script>
      function handleSubmit() {
        const params = new URLSearchParams(location.search);
        const mode = params.get('mode');
        if (mode === 'new') {
          window.location.href = 'bring-guest.html';
        } else {
          // Existing flow can also go to bring guest, adjust as needed
          window.location.href = 'bring-guest.html';
        }
      }
      document.getElementById('newUserBtn').addEventListener('click', () => {
        window.location.href = 'bring-guest.html';
      });
    </script>
  </body>
</html>

