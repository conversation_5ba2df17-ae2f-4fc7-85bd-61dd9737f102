<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <link rel="apple-touch-icon" sizes="180x180" href="./images/favicon/apple-touch-icon.png" />
  <link rel="icon" type="image/png" sizes="32x32" href="./images/favicon/favicon-32x32.png" />
  <link rel="icon" type="image/png" sizes="16x16" href="./images/favicon/favicon-16x16.png" />
  <link rel="manifest" href="./images/favicon/site.webmanifest" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>
    Pyxi - Find Your People Through Real Connections | Social Events & Dinners
  </title>
  <meta name="description"
    content="Join Pyxi to build genuine friendships through curated social experiences. No endless swiping - just intimate dinners and activities with 4-5 carefully matched people in your city." />
  <meta name="keywords"
    content="social connections, make friends, dinner events, social activities, networking, friendship app, real connections, social experiences" />
  <meta property="og:title" content="Pyxi - Find Your People Through Real Connections" />
  <meta property="og:description"
    content="Build genuine friendships through curated social experiences. Join intimate dinners and activities with carefully matched people." />
  <meta property="og:type" content="website" />
  <meta property="og:url" content="https://pyxi.com" />
  <meta property="og:image" content="https://pyxi.com/images/pyxi-social-preview.jpg" />
  <meta name="twitter:card" content="summary_large_image" />
  <meta name="twitter:title" content="Pyxi - Find Your People Through Real Connections" />
  <meta name="twitter:description"
    content="Build genuine friendships through curated social experiences. Join intimate dinners and activities with carefully matched people." />
  <link rel="canonical" href="https://pyxi.com" />
  <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "Pyxi",
        "description": "Social connection platform for meaningful friendships through curated experiences",
        "url": "https://pyxi.com",
        "logo": "https://pyxi.com/images/pyxi-logo.png",
        "foundingDate": "2024",
        "sameAs": [
          "https://instagram.com/pyxi"
        ],
        "address": {
          "@type": "PostalAddress",
          "addressLocality": "London",
          "addressCountry": "GB"
        },
        "serviceType": "Social Networking",
        "areaServed": ["London", "Birmingham", "Manchester", "New York"]
      }
    </script>
  <script src="https://cdn.tailwindcss.com"></script>
  <link
    href="https://fonts.googleapis.com/css2?family=Permanent+Marker&family=Work+Sans:wght@300;400;500;600;700&display=swap"
    rel="stylesheet" />
  <link rel="stylesheet" href="./css/styles.css" />
  <link rel="stylesheet" href="./css/mobile-fixes.css" />
  <link rel="stylesheet" href="./css/pyxi-select-mobile.css" />
  <link rel="stylesheet" href="./css/custom.css" />
  <script src="./js/config.js"></script>
  <script src="./js/navbar.js"></script>
  <script src="./js/pyxi.js"></script>
  <script src="https://embed.typeform.com/next/embed.js"></script>

</head>

<body class="font-sans antialiased bg-gray-900 text-white">
  <!-- Navigation -->
  <nav id="navbar" class="fixed top-0 w-full z-50 transition-all duration-300">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-20">
        <!-- Logo -->
        <div class="flex-shrink-0">
          <img src="./images/pyxi-logo.png" alt="Pyxi" class="h-8 w-auto filter brightness-0 invert cursor-pointer"
            onclick="window.scrollTo({top: 0, behavior: 'smooth'})" />
        </div>

        <!-- Desktop Navigation -->
        <div class="hidden lg:flex items-center space-x-8">
          <a href="about.html" class="text-white hover:text-pyxi-red font-medium transition-colors">About</a>
          <a href="#how-it-works" class="text-white hover:text-pyxi-red font-medium transition-colors">How it works</a>
          <a href="#testimonials" class="text-white hover:text-pyxi-red font-medium transition-colors">Testimonials</a>
        </div>

        <!-- Desktop CTA -->
        <div class="hidden lg:flex items-center space-x-4">
          <button class="bg-pyxi-red text-white px-6 py-3 font-semibold rounded-full hover:bg-red-700 transition-colors"
            onclick="window.open('https://signup.pyxi.ai/quiz', '_blank')">
            Join Your First Dinner
          </button>
          <a href="#returning-diner" onclick="scrollToReturningDiner(); return false;"
            class="border-2 border-pyxi-red text-pyxi-red px-6 py-3 font-semibold rounded-full hover:bg-pyxi-red hover:text-white transition-colors ml-2">
            Returning Guest
          </a>
        </div>

        <!-- Mobile menu button -->
        <button id="mobile-menu-btn" class="lg:hidden text-white p-2">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
          </svg>
        </button>
      </div>
    </div>

    <!-- Mobile menu -->
    <div id="mobile-menu" class="lg:hidden hidden bg-gray-900 border-t border-gray-800">
      <div class="px-4 py-6 space-y-4">
        <a href="about.html" class="block text-white hover:text-pyxi-red font-medium py-2">About</a>
        <a href="#how-it-works" class="block text-white hover:text-pyxi-red font-medium py-2">How it works</a>
        <a href="#testimonials" class="block text-white hover:text-pyxi-red font-medium py-2">Testimonials</a>
        <button
          class="w-full bg-pyxi-red text-white px-6 py-3 font-semibold rounded-full hover:bg-red-700 transition-colors mt-4"
          onclick="scrollToSection('get-started')">
          Join Your First Dinner
        </button>
      </div>
    </div>
  </nav>

  <!-- Hero Section -->
  <section class="relative min-h-screen overflow-hidden pt-16" id="about">
    <!-- Background Video -->
    <video autoplay muted loop playsinline class="absolute inset-0 w-full h-full object-cover z-0">
      <source src="./videos/friends.mp4" type="video/mp4" media="(max-width: 768px)" />
      <source src="./videos/friends.mp4" type="video/mp4" />
      <!-- Fallback image for very slow connections -->
      <img src="./images/hero-fallback.jpg" alt="People connecting at dinner" class="w-full h-full object-cover"
        loading="lazy" />
    </video>

    <!-- Overlay -->
    <div class="absolute inset-0 bg-black bg-opacity-60 z-10"></div>

    <!-- Content -->
    <div class="relative z-20 flex items-center min-h-screen py-4">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
        <div class="max-w-3xl">
          <h1
            class="font-marker text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl text-white leading-none mb-6 sm:mb-8">
            Put Down the Phone.<br />
            <span class="text-pyxi-red">Find Your People.</span>
          </h1>
          <p class="text-lg sm:text-xl lg:text-2xl text-gray-200 mb-8 sm:mb-12 leading-relaxed max-w-2xl">
            Just join our intimate dinners and experiences
            with 4-5 carefully matched strangers. Build genuine connections
            and meaningful friendships. Your perfect social circle is
            waiting!
          </p>

          <!-- Scarcity Alert -->
          <div class="bg-red-600/20 border border-red-500/30 rounded-2xl p-4 mb-6 backdrop-blur-sm inline-flex">
            <div class="flex items-center gap-3">
              <div class="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
              <p class="text-white font-medium" id="scarcity-text">
                Next dinner on Thursday at Canary Wharf!
              </p>
            </div>
          </div>

          <div class="flex flex-col sm:flex-row gap-4">
            <button onclick="window.open('https://signup.pyxi.ai/quiz', '_blank')" data-cta="join-dinner"
              class="bg-pyxi-red text-white px-10 py-5 text-lg font-semibold rounded-full hover:bg-red-700 transition-all shadow-lg">
              Join Your First Dinner
            </button>
            <button onclick="scrollToSection('city-checker')" data-cta="check-location"
              class="border-2 border-white text-white px-10 py-5 text-lg font-semibold rounded-full hover:bg-white hover:text-gray-900 transition-all">
              Check Available Locations
            </button>
            <button id="returning-guest-btn"
              class="border-2 border-pyxi-red text-pyxi-red px-10 py-5 text-lg font-semibold rounded-full hover:bg-pyxi-red hover:text-white transition-all"
              type="button">
              Returning Guest?
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Floating Elements -->
    <div class="absolute top-20 right-10 w-16 h-16 bg-pyxi-red rounded-full opacity-20 animate-bounce z-15"></div>
    <div class="absolute bottom-32 left-20 w-12 h-12 bg-white rounded-full opacity-10 animate-pulse z-15"></div>
    <div class="absolute top-1/2 right-1/4 w-8 h-8 bg-pyxi-red rounded-full opacity-30 animate-ping z-15"></div>
  </section>

  <!-- City Availability Checker -->
  <section id="city-checker" class="bg-gradient-to-br from-gray-800 to-gray-900 py-16 sm:py-20">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <h2 class="font-marker text-3xl sm:text-4xl lg:text-5xl text-white mb-6">
        Find Your <span class="text-pyxi-red">Location</span>
      </h2>
      <p class="text-xl text-gray-300 mb-4">
        Check out Pyxi dinner locations in London
      </p>

      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        <div
          class="bg-green-600/20 border-2 border-green-500 rounded-3xl p-8 text-center hover:bg-green-600/30 transition-all duration-300 transform hover:-translate-y-1">
          <div class="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
            <span class="text-white text-2xl font-bold">✓</span>
          </div>
          <h3 class="font-marker text-lg text-white mb-3 leading-tight">
            Canary Wharf
          </h3>
          <p class="text-green-400 font-semibold mb-6 text-sm">
            Available Now
          </p>
          <button
            class="bg-green-600 text-white px-4 py-2.5 text-sm font-semibold rounded-full hover:bg-green-700 transition-all duration-300 transform hover:scale-105 w-full shadow-md"
            onclick="window.open('https://signup.pyxi.ai/quiz', '_blank')">
            Join In
          </button>
        </div>
        <div
          class="bg-green-600/20 border-2 border-green-500 rounded-3xl p-8 text-center hover:bg-green-600/30 transition-all duration-300 transform hover:-translate-y-1">
          <div class="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
            <span class="text-white text-2xl font-bold">✓</span>
          </div>
          <h3 class="font-marker text-lg text-white mb-3 leading-tight">
            Soho
          </h3>
          <p class="text-green-400 font-semibold mb-6 text-sm">
            Available Now
          </p>
          <button
            class="bg-green-600 text-white px-4 py-2.5 text-sm font-semibold rounded-full hover:bg-green-700 transition-all duration-300 transform hover:scale-105 w-full shadow-md"
            onclick="window.open('https://signup.pyxi.ai/quiz', '_blank')">
            Join In
          </button>
        </div>

        <div
          class="bg-gray-700/30 border-2 border-gray-600 rounded-3xl p-8 text-center hover:bg-gray-700/50 transition-all duration-300 transform hover:-translate-y-1">
          <div class="w-16 h-16 bg-gray-600 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
            <span class="text-white text-2xl">⏳</span>
          </div>
          <h3 class="font-marker text-lg text-white mb-3 leading-tight">
            Islington
          </h3>
          <p class="text-gray-400 font-semibold mb-6 text-sm">Coming Soon</p>
          <button
            class="bg-gray-600 text-white px-4 py-2.5 text-sm font-semibold rounded-full hover:bg-gray-500 transition-all duration-300 transform hover:scale-105 w-full shadow-md"
            onclick="window.open('https://pyxi.typeform.com/to/GHSSCZ4h', '_blank')">
            Join Waitlist
          </button>
        </div>

        <div
          class="bg-gray-700/30 border-2 border-gray-600 rounded-3xl p-8 text-center hover:bg-gray-700/50 transition-all duration-300 transform hover:-translate-y-1">
          <div class="w-16 h-16 bg-gray-600 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
            <span class="text-white text-2xl">⏳</span>
          </div>
          <h3 class="font-marker text-lg text-white mb-3 leading-tight">
            Chelsea
          </h3>
          <p class="text-gray-400 font-semibold mb-6 text-sm">Coming Soon</p>
          <button
            class="bg-gray-600 text-white px-4 py-2.5 text-sm font-semibold rounded-full hover:bg-gray-500 transition-all duration-300 transform hover:scale-105 w-full shadow-md"
            onclick="window.open('https://pyxi.typeform.com/to/GHSSCZ4h', '_blank')">
            Join Waitlist
          </button>
        </div>


      </div>

      <div class="mt-12 bg-pyxi-red/10 border border-pyxi-red/30 rounded-2xl p-6">
        <p class="text-white text-lg">
          Don't see your location?
          <button class="text-pyxi-red font-semibold hover:underline"
            onclick="window.open('https://pyxi.typeform.com/to/GHSSCZ4h', '_blank')">
            Request Your Location
          </button>
          and we'll notify you when we launch!
        </p>
      </div>
    </div>
  </section>

  <!-- Featured In Section -->
  <section class="bg-black py-12">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-8">
        <h2 class="font-marker text-2xl sm:text-3xl text-white mb-4">
          As Featured In
        </h2>
      </div>
      <div class="relative overflow-hidden">
        <div class="flex animate-scroll">
          <div class="flex items-center space-x-16 flex-shrink-0">
            <img src="./images/forbes_logo.png" alt="Forbes"
              class="h-8 w-auto object-contain filter brightness-0 invert opacity-60" />
            <img src="./images/BBC_logo.svg" alt="BBC"
              class="h-8 w-auto object-contain filter brightness-0 invert opacity-60" />
            <img src="./images/bss_logo.png" alt="British Style Society"
              class="h-6 w-auto object-contain filter brightness-0 invert opacity-60" />
            <img src="./images/evening_standard_logo.png" alt="Evening Standard"
              class="h-8 w-auto object-contain filter brightness-0 invert opacity-60" />
          </div>
          <div class="w-16 flex-shrink-0"></div>
          <div class="flex items-center space-x-16 flex-shrink-0">
            <img src="./images/forbes_logo.png" alt="Forbes"
              class="h-8 w-auto object-contain filter brightness-0 invert opacity-60" />
            <img src="./images/BBC_logo.svg" alt="BBC"
              class="h-8 w-auto object-contain filter brightness-0 invert opacity-60" />
            <img src="./images/bss_logo.png" alt="British Style Society"
              class="h-6 w-auto object-contain filter brightness-0 invert opacity-60" />
            <img src="./images/evening_standard_logo.png" alt="Evening Standard"
              class="h-8 w-auto object-contain filter brightness-0 invert opacity-60" />
          </div>
        </div>
      </div>
    </div>
  </section>


  <!-- How It Works Section -->
  <section id="how-it-works" class="bg-gray-800 py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16">
        <h2 class="font-marker text-3xl sm:text-4xl lg:text-5xl text-white mb-6">
          How <span class="text-pyxi-red">It Works</span>
        </h2>
        <p class="text-xl text-gray-300 max-w-2xl mx-auto">
          Three simple steps to building meaningful connections
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div class="text-center">
          <div class="w-20 h-20 bg-pyxi-red rounded-full flex items-center justify-center mx-auto mb-6">
            <span class="text-white text-2xl font-bold">1</span>
          </div>
          <h3 class="font-marker text-2xl text-white mb-4">
            Choose an Experience
          </h3>
          <p class="text-gray-300 leading-relaxed">
            Browse our curated experiences from intimate dinners to cultural
            activities. Pick what excites you most.
          </p>
        </div>

        <div class="text-center">
          <div class="w-20 h-20 bg-pyxi-red rounded-full flex items-center justify-center mx-auto mb-6">
            <span class="text-white text-2xl font-bold">2</span>
          </div>
          <h3 class="font-marker text-2xl text-white mb-4">
            Take Our Personality Test
          </h3>
          <p class="text-gray-300 leading-relaxed">
            Tell us about yourself through our quick personality assessment.
            We'll match you with compatible people who share your vibe.
          </p>
        </div>

        <div class="text-center">
          <div class="w-20 h-20 bg-pyxi-red rounded-full flex items-center justify-center mx-auto mb-6">
            <span class="text-white text-2xl font-bold">3</span>
          </div>
          <h3 class="font-marker text-2xl text-white mb-4">Meet IRL and Connect</h3>
          <p class="text-gray-300 leading-relaxed">
            Show up and enjoy! Meet your matched group in person and build
            genuine connections through shared experiences.
          </p>
        </div>
      </div>
    </div>
  </section>

  <!-- Returning Diner Section -->
  <section id="returning-diner" class="bg-gray-900 py-20">
    <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <h2 class="font-marker text-3xl sm:text-4xl text-white mb-6">
        Already Attended a Dinner with us?
      </h2>
      <p class="text-lg text-gray-300 mb-6">
        Enter your email below.
      </p>
      <form id="returning-diner-form" class="flex flex-col sm:flex-row gap-4 justify-center items-center mb-6">
        <input type="email" id="returning-diner-email" required placeholder="Your email"
          class="px-4 py-3 rounded-full bg-gray-800 text-white border border-gray-600 focus:outline-none focus:ring-2 focus:ring-pyxi-red w-full sm:w-64" />
        <button type="submit"
          class="bg-pyxi-red text-white px-8 py-3 font-semibold rounded-full hover:bg-red-700 transition-colors">
          Submit
        </button>
      </form>
      <div id="diner-dates" class="hidden mt-8">
        <h3 class="font-marker text-2xl text-white mb-4">Choose your next Dinner Date</h3>
        <div id="diner-dates-list" class="flex flex-wrap gap-4 justify-center"></div>
      </div>
    </div>
  </section>

  <!-- Pyxi Select Section -->
  <section class="bg-gray-900 py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-16 items-center">
        <div>
          <h2 class="font-marker text-3xl sm:text-4xl lg:text-5xl text-white mb-6 sm:mb-8">
            Pyxi <span class="text-pyxi-red">Select</span>
          </h2>
          <p class="text-lg sm:text-xl text-gray-300 leading-relaxed mb-6 sm:mb-8">
            Every great friendship begins with a single, shared story. Pyxi
            Select is our canvas for creating them. By pairing our unique
            science of connection with extraordinary, hand-picked experiences,
            we set the stage for something special.
          </p>
          <div class="space-y-4">
            <div
              class="experience-item border-l-4 border-pyxi-red pl-6 py-4 cursor-pointer hover:bg-gray-800/50 rounded-r-lg transition-all duration-300"
              data-image="dinner">
              <h3 class="font-marker text-xl text-white mb-2">
                The Reset Table
              </h3>
              <p class="text-gray-300 text-sm">
                Intimate dinners designed to spark meaningful conversations
              </p>
            </div>
            <div
              class="experience-item border-l-4 border-gray-700 pl-6 py-4 cursor-pointer hover:bg-gray-800/50 hover:border-pyxi-red rounded-r-lg transition-all duration-300"
              data-image="couples">
              <h3 class="font-marker text-xl text-white mb-2">+1 Nights (COMING SOON)</h3>
              <p class="text-gray-300 text-sm">
                Couples and friends dinners designed to deepen connections
              </p>
            </div>
            <div
              class="experience-item border-l-4 border-gray-700 pl-6 py-4 cursor-pointer hover:bg-gray-800/50 hover:border-pyxi-red rounded-r-lg transition-all duration-300"
              data-image="culture">
              <h3 class="font-marker text-xl text-white mb-2">
                The Culture Edit (COMING SOON)
              </h3>
              <p class="text-gray-300 text-sm">
                Museums, Galleries, Theatre and more curated experiences
              </p>
            </div>
          </div>
        </div>
        <div class="bg-gray-800 rounded-3xl overflow-hidden shadow-2xl relative">
          <img id="experience-image" src="./images/reset-table.jpg" alt="Curated experiences"
            class="w-full h-64 sm:h-80 lg:h-96 object-cover transition-all duration-500" />
          <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
          <div class="absolute bottom-6 left-6 right-6">
            <h4 id="experience-title" class="font-marker text-2xl text-white mb-2">
              The Reset Table
            </h4>
            <p id="experience-desc" class="text-gray-200 text-sm">
              Intimate dinners designed to spark meaningful conversations
            </p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Typeform Sign-up Section -->
  <section class="bg-gradient-to-br from-pyxi-red to-orange-600 py-20 sm:py-32 relative" id="get-started">
    <div class="absolute inset-0 bg-black bg-opacity-20"></div>
    <div class="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <h2 class="font-marker text-4xl sm:text-5xl lg:text-6xl text-white mb-8">
        Ready to <span class="text-yellow-300">Get Started?</span>
      </h2>
      <p class="text-xl lg:text-2xl text-white mb-12 leading-relaxed opacity-90">
        Take our quiz below to get matched with great company
      </p>

      <button onclick="window.open('https://signup.pyxi.ai/quiz', '_blank')""
                data-cta=" check-location"
        class="border-2 border-white text-white px-10 py-5 text-lg font-semibold rounded-full hover:bg-white hover:text-gray-900 transition-all">
        Take the Personality Quiz
      </button>
      <p class="text-white text-sm mt-4" id="scarcity-text-2">
        ⚡ Next dinner on Thursday at Canary Wharf!
      </p>


    </div>
    </div>

    <!-- Floating decorative elements -->
    <div class="absolute top-20 left-10 w-20 h-20 bg-white rounded-full opacity-10 animate-pulse"></div>
    <div class="absolute bottom-20 right-10 w-16 h-16 bg-yellow-300 rounded-full opacity-20 animate-bounce"></div>
    <div class="absolute top-1/2 left-1/4 w-8 h-8 bg-white rounded-full opacity-15 animate-ping"></div>
  </section>

  <!-- Testimonials Section -->
  <section id="testimonials" class="bg-gray-800 py-16 sm:py-24">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16">
        <h2 class="font-marker text-3xl sm:text-4xl lg:text-5xl text-white mb-6">
          Real Stories, Real <span class="text-pyxi-red">Connections</span>
        </h2>
        <p class="text-xl text-gray-300 max-w-2xl mx-auto">
          Hear from our members
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <!-- Testimonial 1 -->
        <div
          class="bg-gray-800 rounded-3xl p-8 shadow-2xl hover:shadow-pyxi-red/20 transition-all duration-300 transform hover:-translate-y-2">
          <div class="flex items-center mb-6">
            <picture>
              <source srcset="./images/james.jpg" type="image/webp">
              <img src="./images/james.jpg" alt="James" class="w-16 h-16 rounded-full object-cover mr-4"
                loading="lazy" />
            </picture>
            <div>
              <h4 class="text-white font-semibold text-lg">James</h4>
              <p class="text-gray-400 text-sm">Software Engineer</p>
            </div>
          </div>
          <blockquote class="text-gray-300 leading-relaxed mb-4">
            "Pyxi set us up in an interesting restaurant very close to where I live making it easy to get to. Not only
            that It was a unique place where I was able to try something new which I really enjoyed. The other people’s
            careers and live genuinely interested me and we have been able to keep in touch after. I look forward to
            using Pyxi again."
          </blockquote>
          <div class="flex text-pyxi-red text-sm">⭐⭐⭐⭐⭐</div>
        </div>

        <!-- Testimonial 2 -->
        <div
          class="bg-gray-800 rounded-3xl p-8 shadow-2xl hover:shadow-pyxi-red/20 transition-all duration-300 transform hover:-translate-y-2">
          <div class="flex items-center mb-6">
            <picture>
              <source srcset="./images/serena.jpg" type="image/webp">
              <img src="./images/serena.jpg" alt="Serena" class="w-16 h-16 rounded-full object-cover mr-4"
                loading="lazy" />
            </picture>
            <div>
              <h4 class="text-white font-semibold text-lg">Serena</h4>
              <p class="text-gray-400 text-sm">Sustainability Director</p>
            </div>
          </div>
          <blockquote class="text-gray-300 leading-relaxed mb-4">
            "Working remotely can get really isolating — some days I barely speak to anyone outside of Zoom. Pyxi was
            such a refreshing change. The dinner gave me a chance to meet interesting people in a relaxed setting,
            without the awkwardness of traditional networking. I left feeling energised and more connected"
          </blockquote>
          <div class="flex text-pyxi-red text-sm">⭐⭐⭐⭐⭐</div>
        </div>


        <!-- Testimonial 3 -->
        <div
          class="bg-gray-800 rounded-3xl p-8 shadow-2xl hover:shadow-pyxi-red/20 transition-all duration-300 transform hover:-translate-y-2">
          <div class="flex items-center mb-6">
            <picture>
              <source srcset="./images/natalie.jpg" type="image/webp">
              <img src=./images/natalie.jpg"" alt="Natalie Lawyer" class="w-16 h-16 rounded-full object-cover mr-4"
                loading="lazy" />
            </picture>
            <div>
              <h4 class="text-white font-semibold text-lg">Natalie</h4>
              <p class="text-gray-400 text-sm">Lawyer</p>
            </div>
          </div>
          <blockquote class="text-gray-300 leading-relaxed mb-4">
            "Pyxi dinners are like networking but better. You have deep meaningful conversations with interesting people
            but in a very natural way over a nice meal and wine"
          </blockquote>
          <div class="flex text-pyxi-red text-sm">⭐⭐⭐⭐⭐</div>
        </div>
      </div>
    </div>
  </section>

  <!-- Neighbourhoods Section -->
  <section class="bg-gradient-to-br from-purple-900 to-indigo-900 py-20 sm:py-32 relative overflow-hidden">
    <div class="absolute inset-0 bg-black bg-opacity-30"></div>
    <div class="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <h2 class="font-marker text-4xl sm:text-5xl lg:text-6xl text-white mb-8">
        Introducing <br /><span class="text-pyxi-red">Pyxi Neighbourhoods</span>
      </h2>
      <p class="text-xl lg:text-2xl text-white mb-12 leading-relaxed opacity-90">
        Connect with people in your local area. Build lasting friendships
        right where you live.
      </p>
      <h3 class="font-marker text-2xl sm:text-3xl text-white-900 mb-4">
        Join the Waitlist
      </h3>

      <div class="text-center">

        <div data-tf-live="01JZ5AZEHMA1NSZKFJR8Q4QRW5"></div>

        <p class="text-white-500 text-sm mt-3">
          🎯 Be first to connect in your neighbourhood
        </p>
      </div>

    </div>
  </section>

  <!-- Footer -->
  <footer class="bg-black border-t border-gray-800 py-16 sm:py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
        <div class="lg:col-span-1">
          <img src="./images/pyxi-logo.png" alt="Pyxi" class="h-10 w-auto mb-6 filter brightness-0 invert" />
          <p class="text-gray-300 leading-relaxed mb-6">
            Building meaningful connections through curated experiences.
          </p>
          <div class="flex flex-col gap-3">
            <a href="https://play.google.com/store/apps/details?id=com.pyxida.pyxida&pcampaignid=web_share"
              class="inline-block hover:scale-105 transition-transform">
              <img src="https://upload.wikimedia.org/wikipedia/commons/7/78/Google_Play_Store_badge_EN.svg"
                alt="Get it on Google Play" class="h-12 w-auto" />
            </a>
            <a href="https://apps.apple.com/za/app/pyxi/id1663673322"
              class="inline-block hover:scale-105 transition-transform">
              <img src="https://upload.wikimedia.org/wikipedia/commons/3/3c/Download_on_the_App_Store_Badge.svg"
                alt="Download on the App Store" class="h-12 w-auto" />
            </a>
          </div>
        </div>

        <div>
          <h4 class="font-marker text-lg text-white mb-6">Company</h4>
          <ul class="space-y-3">
            <li>
              <a href="about.html" class="text-gray-300 hover:text-pyxi-red transition-colors">About Us</a>
            </li>
            <li>
              <a href="mailto:<EMAIL>"
                class="text-gray-300 hover:text-pyxi-red transition-colors">Careers</a>
            </li>
            <li>
              <a href="mailto:<EMAIL>" class="text-gray-300 hover:text-pyxi-red transition-colors">Contact
                Us</a>
            </li>
          </ul>
        </div>

        <div>
          <h4 class="font-marker text-lg text-white mb-6">Community</h4>
          <ul class="space-y-3">
            <li>
              <a href="https://partner.pyxi.ai/auth/register/" target="_blank" rel="external noopener noreferrer"
                class="text-gray-300 hover:text-pyxi-red transition-colors">Partner with Us</a>
            </li>
            <li>
              <a href="https://partner.pyxi.ai/auth/login/" target="_blank" rel="external noopener noreferrer"
                class="text-gray-300 hover:text-pyxi-red transition-colors">Existing Partner</a>
            </li>
          </ul>
        </div>

        <div>
          <h4 class="font-marker text-lg text-white mb-6">Legal & Social</h4>
          <ul class="space-y-3 mb-6">
            <li>
              <a href="privacy-policy.html" class="text-gray-300 hover:text-pyxi-red transition-colors">Privacy
                Policy</a>
            </li>
            <li>
              <a href="terms-and-conditions.html" class="text-gray-300 hover:text-pyxi-red transition-colors">Terms & Conditions</a>
            </li>
            <li>
              <a href="community-guidelines.html" class="text-gray-300 hover:text-pyxi-red transition-colors">Community guidelines</a>
            </li>
            <li>
              <a href="mailto:<EMAIL>?subject=Reporting%20inappropriate%20behaviour"  class="text-gray-300 hover:text-pyxi-red transition-colors">Report a guest</a>
            </li>
          </ul>
          <div class="flex space-x-3">
            <a href="https://www.instagram.com/pyxi.ai/"
              class="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-pyxi-red transition-colors group">
              <svg class="w-5 h-5 text-gray-300 group-hover:text-white" fill="currentColor" viewBox="0 0 24 24">
                <path
                  d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z" />
              </svg>
            </a>
          </div>
        </div>
      </div>

      <div
        class="border-t border-gray-800 mt-12 pt-8 flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0">
        <p class="text-gray-400 text-sm">
          &copy; 2024 Pyxi Technologies Ltd. All rights reserved.
        </p>
      </div>
    </div>
  </footer>
  <script>
    // Returning Diner Dates Logic
    function getNextNDates(weekday, n) {
      const dates = [];
      let date = new Date();
      // Set to tomorrow to avoid today if today is the day
      date.setDate(date.getDate() + 1);
      while (dates.length < n) {
        if (date.getDay() === weekday) {
          dates.push(new Date(date));
        }
        date.setDate(date.getDate() + 1);
      }
      return dates;
    }

    document.getElementById('returning-diner-form').addEventListener('submit', async function (e) {
      e.preventDefault();
      const emailInput = document.getElementById('returning-diner-email');
      const submitBtn = this.querySelector('button[type="submit"]');
      const originalBtnText = submitBtn.innerHTML;
      // Show loader and disable button
      submitBtn.disabled = true;
      submitBtn.innerHTML = '<svg class="animate-spin h-5 w-5 mr-2 inline-block text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path></svg>Loading...';

      // Remove any previous error
      let errorMsg = document.getElementById('diner-error-msg');
      if (errorMsg) errorMsg.remove();

      try {
        const email = encodeURIComponent(emailInput.value.trim());
        const response = await fetch(`https://pyxi-v2-stable-dn66uttsza-nw.a.run.app/public/exists?email=${email}`, {
          method: 'GET',
          headers: { 'accept': 'application/json' }
        });
        if (response.status === 200) {
          const data = await response.json();
          if (data.exists) {
            // Hide form
            this.classList.add('hidden');
            // Show dates
            const dinerDatesDiv = document.getElementById('diner-dates');
            const list = document.getElementById('diner-dates-list');
            list.innerHTML = '';
            // Get next 3 Tuesdays (2) and Thursdays (4)
            const tuesdays = getNextNDates(2, 3); // 2 = Tuesday
            const thursdays = getNextNDates(4, 3); // 4 = Thursday
            // Combine and label
            const allDates = [];
            for (let i = 0; i < 3; i++) {
              if (tuesdays[i]) allDates.push({ date: tuesdays[i], label: 'Soho' });
              if (thursdays[i]) allDates.push({ date: thursdays[i], label: 'Canary Wharf' });
            }
            // Sort by date ascending
            allDates.sort((a, b) => a.date - b.date);
            // Render
            allDates.forEach(({ date, label }) => {
              const displayStr = date.toLocaleDateString(undefined, { weekday: 'long', month: 'long', day: 'numeric', year: 'numeric' });
              const dateParam = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
              list.innerHTML += `<div class='diner-date-option group bg-gray-800 rounded-full px-6 py-3 font-semibold text-white shadow hover:bg-pyxi-red hover:text-white transition-all flex items-center gap-2 text-base w-[90%] whitespace-nowrap truncate mx-auto cursor-pointer' data-date="${dateParam}">
                <span class='text-pyxi-red font-bold truncate group-hover:text-white'>${displayStr}</span>
                <span class='text-gray-300 truncate group-hover:text-white'>— ${label}</span>
              </div>`;
            });
            // Add click event listeners to date options
            setTimeout(() => {
              const email = emailInput.value.trim();
              document.querySelectorAll('.diner-date-option').forEach(option => {
                option.addEventListener('click', function () {
                  const date = this.getAttribute('data-date');
                  window.open(`https://signup.pyxi.ai/ticket-date-book?email=${email}&date=${date}`, '_blank');
                });
              });
            }, 0);
            dinerDatesDiv.classList.remove('hidden');
          } else {
            // Should not happen, but fallback error
            let error = document.createElement('div');
            error.id = 'diner-error-msg';
            error.className = 'text-red-500 mt-4';
            error.textContent = 'Email not found. Please try again.';
            this.parentNode.appendChild(error);
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalBtnText;
          }
        } else if (response.status === 404) {
          // Show error message
          let error = document.createElement('div');
          error.id = 'diner-error-msg';
          error.className = 'text-red-500 mt-4';
          error.textContent = 'Email not found. Please try again.';
          this.parentNode.appendChild(error);
          submitBtn.disabled = false;
          submitBtn.innerHTML = originalBtnText;
        } else {
          // Show error message
          let error = document.createElement('div');
          error.id = 'diner-error-msg';
          error.className = 'text-red-500 mt-4';
          error.textContent = 'There was a problem. Please try again.';
          this.parentNode.appendChild(error);
          submitBtn.disabled = false;
          submitBtn.innerHTML = originalBtnText;
        }
      } catch (err) {
        // Show error message
        let error = document.createElement('div');
        error.id = 'diner-error-msg';
        error.className = 'text-red-500 mt-4';
        error.textContent = 'There was a problem. Please try again.';
        this.parentNode.appendChild(error);
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalBtnText;
      }
    });

    // Smooth scroll to returning-diner section
    function scrollToReturningDiner() {
      const section = document.getElementById('returning-diner');
      if (section) {
        section.scrollIntoView({ behavior: 'smooth' });
      }
    }
    document.getElementById('returning-guest-btn').addEventListener('click', scrollToReturningDiner);
  </script>
</body>

</html>