<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Pyxi • Bring a Guest</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Permanent+Marker&family=Work+Sans:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
    <script>
      tailwind.config = {
        theme: {
          extend: {
            fontFamily: { sans: ["Work Sans", "sans-serif"], marker: ["Permanent Marker", "cursive"] },
            colors: { "pyxi-red": "#F09E5B" }
          }
        }
      };
    </script>
  </head>
  <body class="font-sans bg-[#FFF9EC] min-h-screen flex items-start justify-center py-16">
    <div class="w-full max-w-xl bg-white shadow-xl rounded-2xl p-6 md:p-8 border border-gray-100">
      <div class="flex flex-col items-center">
        <img src="./images/pyxi-logo.png" alt="Pyxi" class="h-8 w-auto" />
        <p class="font-marker text-sm text-[#F09E5B] mt-1">The Reset Table</p>
      </div>

      <h1 class="text-center mt-6 text-2xl md:text-3xl font-semibold text-gray-900">Want to bring a guest?</h1>
      <p class="text-center text-gray-500 text-sm mt-1">Share this link or invite them by email below</p>

      <!-- Share link -->
      <div class="mt-6 border rounded-xl p-4 bg-gray-50">
        <p class="text-sm font-medium text-gray-700">Share link</p>
        <p class="text-xs text-gray-500 mt-1">This is your personal invite link — just share it!</p>
        <div class="mt-3 flex items-center gap-2">
          <input id="inviteLink" class="flex-1 px-3 py-2 rounded-lg border bg-white" value="" readonly />
          <button id="copyBtn" class="shrink-0 inline-flex items-center justify-center w-10 h-10 rounded-lg bg-[#F09E5B] text-white hover:opacity-90" title="Copy">📋</button>
        </div>
        <p id="copiedMsg" class="hidden text-green-600 text-xs mt-2">Link copied!</p>
      </div>

      <!-- Email chips -->
      <div class="mt-6">
        <label class="block text-sm font-medium text-gray-700">Email</label>
        <p class="text-xs text-gray-500 mb-2">Enter your guest’s email and we’ll send them a personal invite</p>
        <div id="chips" class="flex flex-wrap gap-2 border rounded-lg p-2 bg-white">
          <input id="emailInput" type="email" placeholder="<EMAIL>" class="outline-none flex-1 min-w-[180px] p-2" />
        </div>
        <button id="sendBtn" class="w-full mt-4 bg-[#F09E5B] text-white font-semibold py-3 rounded-lg hover:opacity-90">Send Invite</button>
        <p id="sendMsg" class="hidden text-green-600 text-sm text-center mt-2">Invites queued. We’ll email your guests shortly.</p>
      </div>
    </div>

    <script>
      // Get order_id from query parameters and set invite link
      function getOrderIdFromUrl() {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get('order_id');
      }

      // Set the invite link with the order_id from query params
      const orderId = getOrderIdFromUrl();
      const inviteLinkInput = document.getElementById('inviteLink');
      if (orderId) {
        inviteLinkInput.value = `https://www.pyxi.ai/dinner-invite.html?order_id=${orderId}`;
      } else {
        inviteLinkInput.value = 'https://www.pyxi.ai/dinner-invite.html?order_id=';
      }

      // Copy link
      document.getElementById('copyBtn').addEventListener('click', () => {
        const input = document.getElementById('inviteLink');
        navigator.clipboard.writeText(input.value).then(() => {
          document.getElementById('copiedMsg').classList.remove('hidden');
          setTimeout(() => document.getElementById('copiedMsg').classList.add('hidden'), 1500);
        });
      });

      // Simple email chips
      const chips = document.getElementById('chips');
      const input = document.getElementById('emailInput');
      function addChip(email) {
        const e = email.trim();
        if (!e) return;
        const chip = document.createElement('span');
        chip.className = 'inline-flex items-center gap-2 px-2.5 py-1 rounded-full bg-gray-100 text-gray-700 text-sm';
        chip.innerHTML = `${e} <button class="text-gray-500 hover:text-gray-700" aria-label="Remove">×</button>`;
        chip.querySelector('button').addEventListener('click', () => chip.remove());
        chips.insertBefore(chip, input);
        input.value = '';
      }
      input.addEventListener('keydown', (ev) => {
        if (ev.key === 'Enter' || ev.key === ',') {
          ev.preventDefault();
          addChip(input.value);
        }
        if (ev.key === 'Backspace' && !input.value) {
          const last = chips.querySelector('span:last-of-type');
          if (last) last.remove();
        }
      });

      // Send (demo)
      document.getElementById('sendBtn').addEventListener('click', () => {
        const emails = Array.from(chips.querySelectorAll('span')).map(s => s.firstChild.textContent.trim());
        if (input.value) emails.push(input.value.trim());
        // In a real app, POST emails to your backend here.
        if (emails.length) {
          document.getElementById('sendMsg').classList.remove('hidden');
          input.value = '';
        }
      });
    </script>
  </body>
</html>

